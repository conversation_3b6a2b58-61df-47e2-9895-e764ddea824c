'use client';

import React, { useState, useEffect } from 'react';
import { runAccessibilityAudit, generateAccessibilityReport } from '@/lib/accessibilityTesting';

/**
 * ♿ ACCESSIBILITY TESTING COMPONENT
 * 
 * Development tool for testing WCAG 2.1 AA compliance
 * Only visible in development mode
 */

const AccessibilityTester = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [auditResults, setAuditResults] = useState(null);
  const [isRunning, setIsRunning] = useState(false);

  // Only show in development
  useEffect(() => {
    setIsVisible(process.env.NODE_ENV === 'development');
  }, []);

  const runAudit = async () => {
    setIsRunning(true);
    try {
      const results = runAccessibilityAudit();
      setAuditResults(results);
    } catch (error) {
      console.error('Accessibility audit failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const downloadReport = () => {
    if (!auditResults) return;
    
    const report = generateAccessibilityReport(auditResults);
    const blob = new Blob([report], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `accessibility-report-${new Date().toISOString().split('T')[0]}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-4 left-4 z-50 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-sm">
      <h3 className="text-sm font-bold mb-2">🔍 Accessibility Tester</h3>
      
      <div className="space-y-2">
        <button
          onClick={runAudit}
          disabled={isRunning}
          className="w-full px-3 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50"
        >
          {isRunning ? 'Running Audit...' : 'Run Accessibility Audit'}
        </button>
        
        {auditResults && (
          <>
            <div className="text-xs space-y-1">
              <div className="flex justify-between">
                <span>Contrast Issues:</span>
                <span className={auditResults.summary.contrastIssues > 0 ? 'text-red-600' : 'text-green-600'}>
                  {auditResults.summary.contrastIssues}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Keyboard Issues:</span>
                <span className={auditResults.summary.keyboardIssues > 0 ? 'text-red-600' : 'text-green-600'}>
                  {auditResults.summary.keyboardIssues}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Image Issues:</span>
                <span className={auditResults.summary.imageIssues > 0 ? 'text-red-600' : 'text-green-600'}>
                  {auditResults.summary.imageIssues}
                </span>
              </div>
              <div className="flex justify-between">
                <span>ARIA Issues:</span>
                <span className={auditResults.summary.ariaIssues > 0 ? 'text-red-600' : 'text-green-600'}>
                  {auditResults.summary.ariaIssues}
                </span>
              </div>
              <div className="flex justify-between font-bold border-t pt-1">
                <span>Total Issues:</span>
                <span className={auditResults.summary.totalIssues > 0 ? 'text-red-600' : 'text-green-600'}>
                  {auditResults.summary.totalIssues}
                </span>
              </div>
            </div>
            
            <button
              onClick={downloadReport}
              className="w-full px-3 py-2 bg-green-600 text-white rounded text-sm hover:bg-green-700"
            >
              Download Report
            </button>
          </>
        )}
      </div>
      
      <div className="mt-3 pt-2 border-t text-xs text-gray-600">
        <p>Development tool only</p>
        <p>Test with keyboard navigation (Tab, Enter, Space)</p>
        <p>Test with screen reader (NVDA, JAWS, VoiceOver)</p>
      </div>
    </div>
  );
};

export default AccessibilityTester;
